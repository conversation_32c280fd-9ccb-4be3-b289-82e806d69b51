<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue";
import textContent from "./textContent.vue";
import videoContent from "./videoContent.vue";

import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import { ElMessage } from "element-plus";
import { onMounted, onUnmounted, ref, watch, nextTick } from "vue";
import { prjForm } from "@/utils/constant";
import type { params4addSection } from "@/apis/path/createProject";
import {
  deleteTextSection,
  deleteVideoSection,
  addVideoSection,
  addTextSection,
} from "@/apis/path/createProject";

const props = defineProps({
  prjId: Number,
  curPrjForm: {
    type: Number,
    required: true,
  },
  chapterList: {
    type: Array,
    required: true,
  },
  releaseDate: {
    type: String,
    required: false,
  },
});
const curChapterId = ref(-1);
const curPrjForm = ref(-1);
const chapterList = ref<any[]>([
  {
    chapterId: -1,
    chapterTitle: "string",
    chapterNum: 1,
  },
]);

enum saveStatus {
  save = 0,
  addChapter = 1,
  saveAndAddChapter = 4, // 保存当前章节后添加新章节 (避免与提交状态2冲突)
}

// 是否正在添加新章节的标志
const isAddingNewChapter = ref(false);

// 初始化章节展开状态
const initChapterExpandState = () => {
  // 如果正在添加新章节，不要重新初始化展开状态
  if (isAddingNewChapter.value) {
    return;
  }

  if (chapterList.value.length === 1) {
    // 只有一个章节时，默认展开第一节
    curChapterId.value = chapterList.value[0].sectionId;
  } else if (chapterList.value.length > 1) {
    // 大于1节时，都默认收起状态
    curChapterId.value = -1;
  }
};

watch(
  () => props,
  (newVal) => {
    if (newVal) {
      chapterList.value = props.chapterList;
      curPrjForm.value = props.curPrjForm;
      // 当章节列表变化时，重新初始化展开状态
      initChapterExpandState();
    }
  },
  { deep: true, immediate: true }
);

// 添加新章节的具体实现
const addNewChapterImpl = async () => {
  // 设置添加新章节标志，防止initChapterExpandState重置展开状态
  isAddingNewChapter.value = true;

  const param = ref<params4addSection>({
    prjId: props.prjId?.toString() || "",
    chapterNum: props.chapterList.length,
  });
  const addChapter =
    curPrjForm.value == prjForm.video ? addVideoSection : addTextSection;
  const res = await addChapter(param.value);
  if (res.success) {
    const chap =
      curPrjForm.value == prjForm.video
        ? {
            longImageUrl: "",
            sectionNum: props.chapterList.length,
            shortVideoUrl: "",
            speechText: "",
            shortImageUrl: "",
            sectionTitle: "这是一个默认的小节标题，请修改",
            sectionId: res.data.WordSectionId ?? res.data.id,
            longVideoUrl: "",
          }
        : {
            sectionId: res.data.WordSectionId ?? res.data.id,
            sectionTitle: "默认标题",
            sectionNum: props.chapterList.length,
            prText: "",
          };
    chapterList.value.push(chap);

    // 使用nextTick确保DOM更新后再设置展开状态
    await nextTick();
    // 新增章节时自动展开新章节
    curChapterId.value = chap.sectionId;

    // 延迟重置标志，确保props更新完成后再重置
    setTimeout(() => {
      isAddingNewChapter.value = false;
    }, 100);

    ElMessage.success("添加成功！");
  } else {
    ElMessage.error(res.message);
    // 失败时立即重置标志
    isAddingNewChapter.value = false;
  }
};

// 处理保存完成
const handleSaveDone = async (data: { status: number; chapterId: number }) => {
  if (data.status === 2 || data.status === 3) {
    // 2: 提交/3: 大的存草稿
    emitter.emit(Event.SAVE_DRAFT_DONE, data.status == 2 ? true : false);
  } else if (data.status === saveStatus.addChapter) {
    // 1: 直接增加小节（当前无展开章节时）
    await addNewChapterImpl();
  } else if (data.status === saveStatus.saveAndAddChapter) {
    // 4: 保存完成后增加小节（当前有展开章节，保存完成后）
    await addNewChapterImpl();
  } else if (data.status === 0) {
    // 0: 存草稿
    curChapterId.value = -1;
    // ElMessage.success("存草稿")
  }
};

// 处理折叠章节
const handleFold = () => {
  if (curChapterId.value == -1) {
    ElMessage.error("当前没有打开的小节你怎么到折叠的！");
  } else {
    emitter.emit(Event.SAVE_CHAPTER, { status: 0, id: curChapterId.value });
  }
};

// 处理打开章节
const handleOpen = (chapterId: number) => {
  if (curChapterId.value != -1) {
    curChapterId.value = chapterId;
  } else {
    curChapterId.value = chapterId;
  }
};

// 处理删除章节
const handleDelete = async (chapterId: number) => {
  if (chapterList.value.length === 1) {
    ElMessage.warning("至少要有一个小节！");
    return;
  }
  const deleteFunc =
    curPrjForm.value == prjForm.video ? deleteVideoSection : deleteTextSection;
  const res = await deleteFunc(chapterId);
  if (res.success) {
    ElMessage.success("删除成功");
    const index = chapterList.value.findIndex((s) => s.sectionId == chapterId);
    chapterList.value.splice(index, 1);
    if (curChapterId.value === chapterId) {
      curChapterId.value = -1;
    }
  } else {
    ElMessage.error("删除失败");
  }
};
// 添加新章节
const addNewChapter = () => {
  if (curChapterId.value == -1) {
    // 当前无展开章节，直接添加
    handleSaveDone({ status: saveStatus.addChapter, chapterId: curChapterId.value });
  } else {
    // 当前有展开章节，先保存再添加
    emitter.emit(Event.SAVE_CHAPTER, {
      status: saveStatus.saveAndAddChapter,
      id: curChapterId.value,
    });
  }
};

const handleSave = (submit?: Boolean) => {
  if (curChapterId.value === -1) {
    emitter.emit(Event.SAVE_DRAFT_DONE, submit ?? false);
    return;
  }
  emitter.emit(Event.SAVE_CHAPTER, {
    status: submit ? 2 : 3,
    id: curChapterId.value,
  });
};

onMounted(() => {
  emitter.on(Event.SAVE_DRAFT, handleSave);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_DRAFT, handleSave);
});
</script>
<template>
  <div class="chapter-wrapper">
    <el-collapse v-model="curChapterId" style="margin-bottom: 40px" accordion>
      <el-collapse-item
        v-for="(chapter, index) in chapterList"
        :name="chapter.sectionId"
        :key="chapter.sectionId"
      >
        <template #title>
          <div class="collapse-title" @click.stop>
            第{{ index + 1 }}节
            <span class="btns">
              <span
                v-if="curChapterId == chapter.sectionId"
                class="b"
                @click="handleFold"
              >
                收起
              </span>
              <span v-else class="b" @click="handleOpen(chapter.sectionId)">
                展开
              </span>
              <span class="b" @click="handleDelete(chapter.sectionId)">
                删除
              </span>
            </span>
          </div>
        </template>
        <div v-if="props.curPrjForm == prjForm.video">
          <video-content
            :chapter="chapter"
            :show-title="true"
            :project-id="props.prjId"
            @save:chapter="handleSaveDone"
            :release-date="props.releaseDate"
          >
          </video-content>
        </div>
        <div v-else>
          <text-content
            :chapter="chapter"
            :show-title="true"
            :project-id="props.prjId"
            @save:chapter="handleSaveDone"
          ></text-content>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div class="add-section-wrapper">
      <my-button :width="145" :height="40" @click="addNewChapter">
        + 新增项目节
      </my-button>
    </div>
  </div>
</template>
<style scoped>
.chapter-wrapper {
  width: 100%;

  :deep(.el-collapse-item__header) {
    height: 100%;
    margin-bottom: 10px;
  }

  .collapse-title {
    font-family: var(--font-family-text);
    display: flex;
    width: 100%;
    justify-content: space-between;
    height: 35px;
    line-height: 35px;
    background-color: var(--color-primary);
    color: white;
    padding: 0 10px;
    cursor: default;

    .btns {
      display: flex;
      width: 88px;
      justify-content: space-between;

      .b {
        cursor: pointer;
      }
    }
  }

  :deep(.el-collapse-item__arrow) {
    display: none;
  }
}
.add-section-wrapper {
  width: 100%;
  margin-bottom: 20px;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  border: 2px dashed var(--color-primary);
}
</style>

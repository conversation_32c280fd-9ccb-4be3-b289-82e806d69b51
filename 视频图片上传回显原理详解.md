# 视频图片上传回显原理详解

## 概述

本文档详细介绍了 Wellerman 前端项目中视频和图片的上传、存储、短长链接转换以及回显的完整原理。系统采用腾讯云 COS (Cloud Object Storage) 作为文件存储服务，通过临时密钥机制确保安全性。

## 核心概念

### 短链接 vs 长链接

- **短链接 (shortUrl/commUrl)**：存储在数据库中的相对路径，不包含域名和签名信息
- **长链接 (longUrl/echoUrl)**：包含完整域名、签名和访问权限的可直接访问的 URL

### 数据流向架构

```mermaid
graph TD
    A[用户选择文件] --> B[前端处理]
    B --> C[获取临时密钥]
    C --> D[上传到 COS]
    D --> E[生成短链接存储到数据库]
    E --> F[通过 COS SDK 生成长链接]
    F --> G[页面显示]
    
    H[页面刷新/数据获取] --> I[从数据库获取短链接]
    I --> J[通过 COS SDK 转换为长链接]
    J --> G
```

## 图片上传回显原理

### 1. 图片上传流程

#### 核心组件：`imgUpload.vue`

```typescript
// 图片上传的关键步骤
const uploadImg = () => {
  isLoading.value = true
  // 获取裁剪后的图片 Blob
  cropper.value.getCropBlob(async (data: Blob) => {
    let formData = new FormData()
    formData.append("file", data)
    formData.append("prjId", prjId.value.toString())
    if (sectionId.value) {
      formData.append("sectionId", sectionId.value.toString())
    }
    
    // 直接调用后端 API 上传
    const res = await axios({
      method: "post",
      url: "/wellerman-service/cos/file/imageCosUpload",
      data: formData,
    })
    
    if (res.data.success) {
      shortUrl.value = res.data.data.shortUrl  // 存储到数据库
      longUrl.value = res.data.data.longUrl    // 用于页面显示
      emit("sendImgUrl", shortUrl.value, longUrl.value)
    }
  })
}
```

#### 图片上传时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant ImgUpload as imgUpload.vue
    participant Backend as 后端 API
    participant COS as 腾讯云 COS
    participant DB as 数据库

    User->>ImgUpload: 选择图片
    ImgUpload->>ImgUpload: 图片裁剪处理
    User->>ImgUpload: 点击确认上传
    ImgUpload->>Backend: POST /cos/file/imageCosUpload
    Backend->>COS: 上传图片文件
    COS-->>Backend: 返回存储路径
    Backend->>DB: 存储短链接
    Backend-->>ImgUpload: 返回 {shortUrl, longUrl}
    ImgUpload->>ImgUpload: 更新显示 (longUrl)
    ImgUpload->>Parent: emit("sendImgUrl", shortUrl, longUrl)
```

### 2. 图片回显流程

```typescript
// 从 getVideoSection API 获取数据后的处理
const initData = () => {
  getVideoSection(ruleForm.sectionId).then((res) => {
    if (res.success) {
      // 获取短链接和长链接
      ruleForm.videoCover.echoUrl = res.data.list[0].longImageUrl;  // 长链接用于显示
      ruleForm.videoCover.commUrl = res.data.list[0].shortImageUrl; // 短链接用于存储
    }
  });
};
```

## 视频上传回显原理

### 1. 视频上传流程

#### 核心组件：`videoUploader.vue`

```typescript
const uploadVideo = async (file: UploadRawFile) => {
  // 1. 构建存储路径
  let key = buildKey(
    props.releaseDate,    // 发布日期
    props.projectId,      // 项目ID
    props.sectionId,      // 章节ID
    "video",              // 文件类型
    file.name             // 文件名
  );
  
  // 2. 设置项目ID用于获取临时密钥
  setPrjId4cosKey(props.projectId);
  
  // 3. 分片上传到 COS
  const uploadTask = sliceUpload(file, key, percentage, taskId, taskKey);
  
  // 4. 上传完成后通知后端
  uploadTask.promise.then((res) => {
    uploadVideo2Backend(props.sectionId, key).then((res) => {
      if (res.success) {
        console.log("视频的key值成功传给后端");
        getVideoName(key);
      }
    });
    emits("sendVideoKey", key);
  });
};
```

#### 视频上传时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant VideoUploader as videoUploader.vue
    participant COS_Utils as cos.ts
    participant COS as 腾讯云 COS
    participant Backend as 后端 API
    participant DB as 数据库

    User->>VideoUploader: 选择视频文件
    VideoUploader->>COS_Utils: buildKey() 生成存储路径
    VideoUploader->>COS_Utils: setPrjId4cosKey() 设置项目ID
    VideoUploader->>COS_Utils: sliceUpload() 分片上传
    COS_Utils->>Backend: getTmpSecretKey() 获取临时密钥
    Backend-->>COS_Utils: 返回临时密钥
    COS_Utils->>COS: 分片上传视频文件
    COS-->>COS_Utils: 上传进度回调
    COS_Utils-->>VideoUploader: 更新进度条
    COS-->>COS_Utils: 上传完成
    COS_Utils-->>VideoUploader: 上传成功
    VideoUploader->>Backend: uploadVideo2Backend() 通知后端
    Backend->>DB: 存储视频路径 (短链接)
    VideoUploader->>Parent: emit("sendVideoKey", key)
```

### 2. 视频回显流程

```typescript
// 视频回显的关键步骤
watch(() => props, (newVal) => {
  if (newVal.videoKey) {
    getVideoName(newVal.videoKey);
    setPrjId4cosKey(newVal.projectId);
    hasUploadHistory.value = true;
    
    // 获取视频信息
    getVideoInfo(newVal.videoKey).then((res) => {
      if (res.Response.MediaInfo.Format.Duration) {
        videoDuration.value = res.Response.MediaInfo.Format.Duration;
        taskKey.value = newVal.videoKey;
        percentage.value = 100;
      }
    });
  }
}, { deep: true, immediate: true });
```

## COS 临时密钥机制

### 1. 临时密钥获取

#### 核心文件：`cos.ts`

```typescript
const cos = new COS({
  getAuthorization: (options, callback) => {
    getTmpSecretKey(prjId.value).then((res) => {
      if (res.success) {
        callback({
          TmpSecretId: res.data.TmpSecretId,
          TmpSecretKey: res.data.TmpSecretKey,
          SecurityToken: res.data.SecurityToken,
          StartTime: 1712659517,
          ExpiredTime: 1812659517,
        });
      }
    });
  },
});
```

### 2. 短链接到长链接转换

```typescript
// 获取带签名的访问 URL
export const getVideoUrl = (
  bucketName: string,
  bucketRegion: string,
  storeKey: string,        // 短链接 (存储路径)
  resultUrl: Ref           // 长链接 (输出)
) => {
  cos.getObjectUrl({
    Bucket: bucketName,
    Region: bucketRegion,
    Key: storeKey,           // 短链接作为输入
    Sign: true,              // 生成带签名的 URL
    Domain: import.meta.env.VITE_COS_DOMAIN,
    Protocol: "https:",
  }, function (err, data) {
    if (!err) {
      const url = data.Url;
      const res = url + 
        (url.indexOf("?") > -1 ? "&" : "?") + 
        "response-content-disposition=inline";
      resultUrl.value = res;  // 长链接输出
    }
  });
};
```

## 数据存储结构

### getVideoSection API 返回的数据结构

```typescript
// API: GET /video/detailContent/{sectionId}
{
  success: true,
  data: {
    list: [{
      longImageUrl: "https://cos-domain.com/path/to/image?signature=xxx",  // 长链接
      shortImageUrl: "2024-01-01/123/456/image/uuid_filename.jpg",         // 短链接
      longVideoUrl: "https://cos-domain.com/path/to/video?signature=xxx",  // 长链接
      shortVideoUrl: "2024-01-01/123/456/video/uuid_filename.mp4",         // 短链接
      sectionTitle: "章节标题",
      speechText: [...]  // 视频文稿数据
    }]
  }
}
```

### 存储路径规则

```typescript
// buildKey 函数生成的路径格式
const buildKey = (date, prjId, sectionId, fileType, fileName) => {
  const uniqueId = uuid4();
  return `${date}/${prjId}/${sectionId}/${fileType}/${uniqueId}_${fileName}`;
};

// 示例路径：
// "2024-01-15/123/456/video/a1b2c3d4-e5f6-7890-abcd-ef1234567890_myvideo.mp4"
// "2024-01-15/123/456/image/a1b2c3d4-e5f6-7890-abcd-ef1234567890_cover.jpg"
```

## 关键技术点

### 1. 安全性保障

- **临时密钥**：通过 `getTmpSecretKey` API 获取有时效性的访问凭证
- **项目隔离**：每个项目使用独立的存储路径和权限
- **签名验证**：所有访问 URL 都包含签名信息

### 2. 性能优化

- **分片上传**：大文件采用分片上传，支持断点续传
- **进度显示**：实时显示上传进度
- **缓存机制**：长链接生成后缓存使用

### 3. 用户体验

- **图片裁剪**：上传前支持图片裁剪功能
- **预览功能**：上传完成后立即显示预览
- **错误处理**：完善的错误提示和重试机制

## 视频处理特殊机制

### 1. 视频格式转换

```typescript
// 视频上传后的处理流程
const polling4resKey = () => {
  getM3U8Key(mp4VideoKey.value).then((res) => {
    if (res.success) {
      videoSrc.value = res.data.m3u8Key  // 获取转换后的 m3u8 格式
    } else {
      startPolling()  // 转换未完成，开始轮询
    }
  })
}

// 轮询机制等待视频处理完成
const startPolling = () => {
  timeInterval = setInterval(() => {
    getM3U8Key(mp4VideoKey.value).then((res) => {
      if (res.success) {
        videoSrc.value = res.data.m3u8Key
        clearInterval(timeInterval)
      } else {
        percentage.value = res.data.progress  // 显示处理进度
      }
    })
  }, 5000)
}
```

### 2. 视频截图生成

```typescript
// 生成视频预览截图
const getVideoCutsFromCos = (key: string, num: number) => {
  const tickList = generateRandomTick(videoDuration.value, num);
  const tempUrlList = ref<any[]>([]);
  setPrjId4cosKey(props.projectId);

  for (let i = 0; i < num; i++) {
    getVideoCut(key, tickList[i]).then((result) => {
      tempUrlList.value.push(URL.createObjectURL(result.Body as Blob));
    });
  }
  imgUrlList.value = tempUrlList.value;
};

// COS 视频截图 API
export const getVideoCut = (storeKey: string, tick: number): Promise<any> => {
  return new Promise((resolve, reject) => {
    cos.request({
      Bucket: import.meta.env.VITE_COS_BUCKET_NAME,
      Region: import.meta.env.VITE_COS_REGION,
      Key: storeKey,
      Method: "GET",
      Query: {
        "ci-process": "snapshot",  // 腾讯云 CI 截图处理
        time: tick,                // 截图时间点（秒）
      },
    }, function (err, data) {
      if (err) reject(err);
      else resolve(data);
    });
  });
};
```

## 错误处理和用户体验

### 1. 上传状态管理

```typescript
// 视频上传状态管理
const uploadVideo = async (file: UploadRawFile) => {
  // 显示进度条和取消按钮
  isProgressShow.value = true;
  showCancelBtn.value = true;
  hasUploadHistory.value = true;

  try {
    const uploadTask = sliceUpload(file, key, percentage, taskId, taskKey);
    currentUploadTask.value = uploadTask;

    uploadTask.promise
      .then((res) => {
        showCancelBtn.value = false;  // 隐藏取消按钮
        currentUploadTask.value = null;
        // 通知后端保存视频信息
        uploadVideo2Backend(props.sectionId, key);
        emits("sendVideoKey", key);
      })
      .catch((error: any) => {
        if (error.message !== "USER_CANCELLED") {
          ElMessage.error("上传失败");
        }
        cleanupUploadTask();
      });
  } catch (err) {
    cleanupUploadTask();
  }
};

// 取消上传任务
const cancelUpload = () => {
  if (currentUploadTask.value) {
    currentUploadTask.value.cancel();
    cleanupUploadTask();
  }
};
```

### 2. 图片裁剪功能

```typescript
// 图片裁剪配置
const option = reactive({
  img: "", // 裁剪图片的地址
  outputType: "jpeg", // 裁剪生成图片的格式
  full: false, // 是否输出原图比例的截图
  canMoveBox: false, // 截图框能否拖动
  original: false, // 上传图片按照原始比例渲染
  autoCrop: true, // 是否默认生成截图框
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [4, 3], // 截图框的宽高比例
  centerBox: false, // 截图框是否被限制在图片里面
  infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
  fixedBox: false, // 固定截图框大小 不允许改变
});

// 处理图片选择
const handleChange = (UploadFile: UploadFile) => {
  upLoadFile.value = UploadFile
  centerDialogVisible.value = true

  // 将图片转化为 base64 格式用于裁剪
  const reader = new FileReader()
  reader.onload = (e) => {
    option.img = e.target.result  // 绑定到裁剪组件
  }
  reader.readAsDataURL(UploadFile.raw as UploadRawFile)
}
```

## 完整的数据流向图

```mermaid
graph TB
    subgraph "用户操作"
        A1[选择图片] --> A2[图片裁剪]
        A3[选择视频] --> A4[视频上传]
    end

    subgraph "前端处理"
        A2 --> B1[imgUpload.vue]
        A4 --> B2[videoUploader.vue]
        B1 --> B3[调用后端 API]
        B2 --> B4[COS 分片上传]
    end

    subgraph "后端服务"
        B3 --> C1[图片上传 API]
        B4 --> C2[获取临时密钥 API]
        C1 --> C3[上传到 COS]
        C2 --> C4[返回临时凭证]
    end

    subgraph "腾讯云 COS"
        C3 --> D1[存储图片文件]
        C4 --> D2[验证权限]
        D2 --> D3[分片上传视频]
        D3 --> D4[视频格式转换]
        D4 --> D5[生成 M3U8]
    end

    subgraph "数据库存储"
        D1 --> E1[保存图片短链接]
        D5 --> E2[保存视频短链接]
    end

    subgraph "页面回显"
        E1 --> F1[getVideoSection API]
        E2 --> F1
        F1 --> F2[获取短链接]
        F2 --> F3[COS SDK 转换]
        F3 --> F4[生成长链接]
        F4 --> F5[页面显示]
    end
```

## API 接口详解

### 1. 关键 API 列表

| API 路径 | 方法 | 功能 | 输入 | 输出 |
|---------|------|------|------|------|
| `/cos-sts-client/getTemporaryKey/{prjId}` | GET | 获取临时密钥 | 项目ID | 临时凭证 |
| `/cos/file/imageCosUpload` | POST | 图片上传 | FormData | 短长链接 |
| `/video/updateVideo` | POST | 保存视频信息 | sectionId, url | 成功状态 |
| `/video/getProcessedKey` | POST | 获取处理后视频 | mp4Key | m3u8Key |
| `/video/detailContent/{sectionId}` | GET | 获取视频详情 | 章节ID | 完整数据 |

### 2. 临时密钥 API 详解

```typescript
// API: GET /cos-sts-client/getTemporaryKey/{prjId}
// 响应数据结构
{
  success: true,
  data: {
    TmpSecretId: "临时访问密钥ID",
    TmpSecretKey: "临时访问密钥",
    SecurityToken: "安全令牌",
    StartTime: 1712659517,    // 开始时间戳
    ExpiredTime: 1812659517   // 过期时间戳
  }
}
```

### 3. 视频处理 API 详解

```typescript
// API: POST /video/getProcessedKey
// 请求数据
{
  mp4Key: "2024-01-15/123/456/video/uuid_filename.mp4"
}

// 响应数据（处理完成）
{
  success: true,
  data: {
    m3u8Key: "processed_video_url.m3u8"
  }
}

// 响应数据（处理中）
{
  success: false,
  data: {
    progress: 65  // 处理进度百分比
  }
}
```

## 总结

整个系统通过以下关键步骤实现文件的上传和回显：

### 上传阶段
1. **文件选择** → 前端组件处理
2. **权限获取** → 临时密钥 API
3. **文件上传** → COS 存储服务
4. **路径存储** → 数据库保存短链接
5. **即时显示** → 生成长链接用于预览

### 回显阶段
1. **数据获取** → API 返回短链接
2. **权限验证** → 临时密钥机制
3. **链接转换** → COS SDK 生成长链接
4. **页面渲染** → 显示图片/视频内容

### 核心优势
- **安全性**：临时密钥 + 签名验证
- **可维护性**：短链接存储 + 动态生成
- **用户体验**：实时进度 + 即时预览
- **性能优化**：分片上传 + 格式转换

这种架构设计既满足了大文件上传的性能需求，又保证了系统的安全性和可扩展性。

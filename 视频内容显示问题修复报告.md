# 视频内容显示问题修复报告

## 问题描述

在项目 `d:\WorkPlace\wellerman_frontend_3.0` 中，`src\views\createProject\step2v2\index.vue` 页面存在数据显示问题：

- 页面加载时正常调用 `getProjectDetail` 接口并获取数据
- 后端接口正常返回了项目数据
- 但在 `src\views\createProject\step2v2\videoContent.vue` 组件中，**封面、视频和视频文稿**这三项内容无法正确显示
- 对比发现 `src\views\projectPreview\components\prjPreview.vue` 中的相同功能工作正常

## 根本原因分析

### 1. 数据流向问题

通过分析代码发现，数据流向存在断层：

```mermaid
sequenceDiagram
    participant Index as index.vue
    participant Multi as multiContentWrapper.vue
    participant VideoContent as videoContent.vue (step2v2)
    participant API as getProjectDetail API
    participant VideoAPI as getVideoSection API

    Note over Index: 页面加载
    Index->>API: getProjectDetail(prjId)
    API-->>Index: 返回项目数据 (chapterList)
    Index->>Multi: 传递 chapterList
    Multi->>VideoContent: 传递 chapter 对象

    Note over VideoContent: 问题出现在这里
    VideoContent->>VideoContent: Object.assign(ruleForm, chapter)
    Note over VideoContent: 但 chapter 数据结构与 ruleForm 不匹配

    Note over VideoContent: 正常流程应该是：
    VideoContent->>VideoAPI: getVideoSection(sectionId)
    VideoAPI-->>VideoContent: 返回视频详细数据
    VideoContent->>VideoContent: 更新 ruleForm 数据
```

### 2. 数据结构不匹配

**问题组件 (`step2v2/videoContent.vue`)**：
- 使用 `videoChapterType` 类型
- 接收 `chapter` 对象作为 props
- 直接使用 `Object.assign(ruleForm, newValue.chapter)` 赋值
- **缺少详细数据获取逻辑**

**正常组件 (`step2/videoContent.vue`)**：
- 使用 `videoSectionType` 类型  
- 接收 `projectId`, `sectionId` 等单独 props
- 有完整的 `initData()` 方法调用 `getVideoSection` API
- **能够获取完整的视频详细信息**

### 3. 关键差异对比

| 方面 | step2v2/videoContent.vue (问题) | step2/videoContent.vue (正常) |
|------|--------------------------------|------------------------------|
| 数据类型 | `videoChapterType` | `videoSectionType` |
| Props 接收 | `chapter: Object` | `projectId, sectionId` 等 |
| 数据初始化 | `Object.assign()` 直接赋值 | `initData()` 调用 API |
| API 调用 | ❌ 被注释掉 | ✅ 正常调用 `getVideoSection` |
| 数据完整性 | ❌ 只有基础信息 | ✅ 包含视频详细信息 |

## 修复方案

### 核心思路

恢复 `step2v2/videoContent.vue` 中被注释的 `initData()` 方法，让组件能够正确获取视频详细信息。

### 具体修复步骤

#### 1. 恢复 initData 方法

```typescript
const initData = () => {
  console.log("initData called with sectionId:", ruleForm.sectionId);
  if (ruleForm.sectionId && ruleForm.sectionId !== -1) {
    console.log("Fetching video section data for sectionId:", ruleForm.sectionId);
    getVideoSection(ruleForm.sectionId).then((res) => {
      if (res.success) {
        console.log("Video section data received:", res.data);
        // 更新视频详细信息
        ruleForm.videoCover.echoUrl = res.data.list[0].longImageUrl;
        ruleForm.videoCover.commUrl = res.data.list[0].shortImageUrl;
        ruleForm.videoKey = res.data.list[0].longVideoUrl;
        ruleForm.videoFlag = res.data.list[0].longVideoUrl;
        ruleForm.lecture = res.data.list[0].speechText;
        ruleForm.lectureFlag = res.data.list[0].speechText.length === 0 ? "" : "true";
        ruleForm.sectionTitle = res.data.list[0].sectionTitle || "默认标题";
        
        // 初始化标题编辑器状态
        if (ruleForm.sectionTitle && ruleForm.sectionTitle.trim()) {
          ruleForm.sectionTitle = revertMathScriptsToMd(ruleForm.sectionTitle);
          sectionTitleEditMode.value = false;
        } else {
          sectionTitleEditMode.value = true;
        }
      } else {
        console.error("Failed to fetch video section:", res.message);
        ElMessage.error(res.message);
      }
    }).catch((error) => {
      console.error("Error fetching video section:", error);
    });
  }
};
```

#### 2. 修改 watch 监听器

```typescript
watch(
  () => props,
  (newValue) => {
    console.log("Props changed:", newValue);
    if (newValue.chapter) {
      console.log("Chapter data received:", newValue.chapter);
      // 先设置基本的 chapter 信息
      Object.assign(ruleForm, newValue.chapter);
      // 检查 sectionId 是否为 -1，如果不是，则执行 initData 获取详细数据
      if (ruleForm.sectionId !== -1) {
        initData(); // 关键修复：调用 initData 获取详细数据
      }
    }
  },
  { deep: true, immediate: true }
);
```

#### 3. 修复 TypeScript 类型错误

```typescript
// 修复 props 可能为 undefined 的类型错误
:project-id="props.projectId || 0"
:section-id="ruleForm.sectionId || 0"
```

#### 4. 添加调试信息

为了便于问题排查，添加了详细的 console.log 输出：
- Props 变化监听
- API 调用状态
- 数据更新确认
- 错误信息捕获

## 修复效果

修复后的数据流向：

```mermaid
graph TD
    A[index.vue 获取项目数据] --> B[multiContentWrapper.vue 传递 chapter]
    B --> C[videoContent.vue 接收 chapter]
    C --> D[Object.assign 设置基础信息]
    D --> E{sectionId 是否有效?}
    E -->|是| F[调用 initData()]
    F --> G[getVideoSection API]
    G --> H[更新完整视频数据]
    H --> I[正确显示封面、视频、文稿]
    E -->|否| J[跳过数据获取]
```

## 验证方法

1. **浏览器控制台检查**：
   - 查看是否有 "initData called with sectionId" 日志
   - 确认 "Video section data received" 日志
   - 检查是否有错误信息

2. **页面功能验证**：
   - 视频封面是否正确显示
   - 视频播放器是否正常加载
   - 视频文稿内容是否显示

3. **对比测试**：
   - 与 `projectPreview` 页面的相同功能对比
   - 确保修复后行为一致

## 总结

这个问题的核心是**数据获取逻辑缺失**。`step2v2/videoContent.vue` 组件只接收了基础的 chapter 信息，但没有调用相应的 API 获取视频的详细信息（封面、视频文件、文稿等）。

通过恢复 `initData()` 方法并在适当时机调用，组件现在能够：
1. 接收基础的 chapter 信息
2. 根据 sectionId 调用 `getVideoSection` API
3. 获取完整的视频详细数据
4. 正确显示所有视频相关内容

这个修复方案保持了组件的原有架构，只是恢复了被注释掉的关键数据获取逻辑，确保了数据的完整性和显示的正确性。
